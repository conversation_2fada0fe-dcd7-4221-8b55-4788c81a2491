from typing import Dict, Any, Optional, List
import logging
from datetime import datetime

from fastapi import Depends, HTTPException, Header, APIRouter, Query, Path
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.dao.order import order_dao
from app.dao.coupon import coupon_usage_record_dao
from app.models.order import OrderStatus, PaymentStatus

# 获取logger
logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("")
async def get_user_orders(
    token: Optional[str] = Header(None),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的记录数限制"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取指定用户的所有订单列表
    
    Args:
        token: 用户token
        skip: 跳过的记录数
        limit: 返回的记录数限制
        db: 数据库会话
        
    Returns:
        Dict: 包含订单列表的响应
    """
    logger.info(f"获取用户订单列表，skip: {skip}, limit: {limit}")
    
    # 验证用户token
    if not token:
        logger.warning("请求中未提供token")
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "code": 401}
        )
    
    user = WeChatUserService.verify_token(db, token)
    if not user:
        logger.warning(f"token无效或已过期: {token[:10]}...")
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "code": 401}
        )
    
    logger.info(f"token验证成功，用户ID: {user.id}")
    
    try:
        # 获取用户订单列表
        orders = order_dao.get_by_user(db, user.id, skip=skip, limit=limit)
        logger.info(f"查询到 {len(orders)} 个订单")
        
        # 构建订单列表响应数据
        order_list = []
        for order in orders:
            order_item = {
                "id": order.id,
                "order_no": order.order_no,
                "type": order.type.value,
                "status": order.status.value,
                "payment_status": order.payment_status.value,
                "total_amount": order.total_amount,
                "payable_amount": order.payable_amount,
                "actual_amount_paid": order.actual_amount_paid,
                "payment_method": order.payment_method.value,
                "created_at": order.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "updated_at": order.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
                "payment_time": order.payment_time.strftime("%Y-%m-%d %H:%M:%S") if order.payment_time else None
            }
            order_list.append(order_item)
        
        return {
            "code": 200,
            "message": "success",
            "data": {
                "total": len(order_list),
                "orders": order_list
            }
        }
        
    except Exception as e:
        logger.error(f"获取用户订单列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"message": f"获取订单列表失败: {str(e)}", "code": 500}
        )


@router.get("/{order_id}")
async def get_order_detail(
    order_id: int = Path(..., description="订单ID"),
    token: Optional[str] = Header(None),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取指定用户指定订单的详情
    
    Args:
        order_id: 订单ID
        token: 用户token
        db: 数据库会话
        
    Returns:
        Dict: 包含订单详情的响应
    """
    logger.info(f"获取订单详情，订单ID: {order_id}")
    
    # 验证用户token
    if not token:
        logger.warning("请求中未提供token")
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "code": 401}
        )
    
    user = WeChatUserService.verify_token(db, token)
    if not user:
        logger.warning(f"token无效或已过期: {token[:10]}...")
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "code": 401}
        )
    
    logger.info(f"token验证成功，用户ID: {user.id}")
    
    try:
        # 获取订单详情
        order = order_dao.get(db, order_id)
        if not order:
            logger.warning(f"订单不存在: {order_id}")
            raise HTTPException(
                status_code=404,
                detail={"message": "订单不存在", "code": 404}
            )
        
        # 验证订单属于当前用户
        if order.user_id != user.id:
            logger.warning(f"用户 {user.id} 尝试访问不属于自己的订单 {order_id}")
            raise HTTPException(
                status_code=403,
                detail={"message": "无权访问此订单", "code": 403}
            )
        
        # 构建基本订单信息
        order_detail = {
            "id": order.id,
            "order_no": order.order_no,
            "type": order.type.value,
            "status": order.status.value,
            "payment_status": order.payment_status.value,
            "total_amount": order.total_amount,
            "payable_amount": order.payable_amount,
            "actual_amount_paid": order.actual_amount_paid,
            "payment_method": order.payment_method.value,
            "created_at": order.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": order.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
            "payment_time": order.payment_time.strftime("%Y-%m-%d %H:%M:%S") if order.payment_time else None,
            "pricing_remark": order.pricing_remark or ""
        }
        
        # 获取订单项目信息
        order_items = []
        for item in order.items:
            item_detail = {
                "id": item.id,
                "product_id": item.product_id,
                "product_name": item.product.name if item.product else "未知商品",
                "quantity": item.quantity,
                "price": item.price,
                "subtotal": item.subtotal,
                "final_price": item.final_price,
                "payable_amount": item.payable_amount,
                "pricing_remark": item.pricing_remark or ""
            }
            order_items.append(item_detail)
        
        order_detail["items"] = order_items
        
        # 获取优惠券使用记录
        coupon_records = coupon_usage_record_dao.get_by_order(db, order_id)
        coupon_discounts = []
        total_discount_amount = 0.0
        
        for record in coupon_records:
            coupon_detail = {
                "coupon_usage_record_id": record.id,
                "coupon_id": record.coupon_id,
                "coupon_name": record.coupon.name if record.coupon else "未知优惠券",
                "discount_amount": record.discount_amount,
                "used_at": record.used_at.strftime("%Y-%m-%d %H:%M:%S") if record.used_at else None
            }
            coupon_discounts.append(coupon_detail)
            total_discount_amount += record.discount_amount
        
        order_detail["coupon_discounts"] = coupon_discounts
        order_detail["total_discount_amount"] = total_discount_amount
        
        return {
            "code": 200,
            "message": "success",
            "data": order_detail
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取订单详情失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"message": f"获取订单详情失败: {str(e)}", "code": 500}
        )


@router.post("/{order_id}/cancel")
async def cancel_order(
    order_id: int = Path(..., description="订单ID"),
    token: Optional[str] = Header(None),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    取消指定用户指定订单
    
    Args:
        order_id: 订单ID
        token: 用户token
        db: 数据库会话
        
    Returns:
        Dict: 包含取消结果的响应
    """
    logger.info(f"取消订单，订单ID: {order_id}")
    
    # 验证用户token
    if not token:
        logger.warning("请求中未提供token")
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "code": 401}
        )
    
    user = WeChatUserService.verify_token(db, token)
    if not user:
        logger.warning(f"token无效或已过期: {token[:10]}...")
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "code": 401}
        )
    
    logger.info(f"token验证成功，用户ID: {user.id}")
    
    try:
        # 获取订单
        order = order_dao.get(db, order_id)
        if not order:
            logger.warning(f"订单不存在: {order_id}")
            raise HTTPException(
                status_code=404,
                detail={"message": "订单不存在", "code": 404}
            )
        
        # 验证订单属于当前用户
        if order.user_id != user.id:
            logger.warning(f"用户 {user.id} 尝试取消不属于自己的订单 {order_id}")
            raise HTTPException(
                status_code=403,
                detail={"message": "无权操作此订单", "code": 403}
            )
        
        # 检查订单状态是否允许取消
        if order.status in [OrderStatus.CANCELLED, OrderStatus.REFUNDED, OrderStatus.COMPLETED]:
            logger.warning(f"订单 {order_id} 状态为 {order.status.value}，不允许取消")
            raise HTTPException(
                status_code=400,
                detail={"message": f"订单状态为{order.status.value}，不允许取消", "code": 400}
            )
        
        # 取消订单
        cancelled_order = order_dao.cancel_order(db, order_id)
        if not cancelled_order:
            logger.error(f"取消订单失败: {order_id}")
            raise HTTPException(
                status_code=500,
                detail={"message": "取消订单失败", "code": 500}
            )
        
        logger.info(f"订单 {order_id} 取消成功")
        
        return {
            "code": 200,
            "message": "订单取消成功",
            "data": {
                "order_id": order_id,
                "order_no": cancelled_order.order_no,
                "status": cancelled_order.status.value,
                "payment_status": cancelled_order.payment_status.value,
                "cancelled_at": cancelled_order.updated_at.strftime("%Y-%m-%d %H:%M:%S")
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消订单失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"message": f"取消订单失败: {str(e)}", "code": 500}
        )
